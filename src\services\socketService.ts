import { io, Socket } from 'socket.io-client';

// Define the server URL - this should be your backend WebSocket server
// Auto-detect environment and use appropriate URL
const getServerUrl = () => {
  // Check if we're in development (localhost)
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    const savedPort = localStorage.getItem('thunee_server_port');
    return `http://localhost:${savedPort || '3001'}`;
  }

  // Production environment - use the same host and port as the frontend
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = window.location.port;

  // For IIS deployment, the Node.js server will be on the same host and port
  return `${protocol}//${hostname}${port ? ':' + port : ''}`;
};

let SOCKET_SERVER_URL = getServerUrl();

class SocketService {
  private socket: Socket | null = null;
  private listeners: Map<string, Array<(data: any) => void>> = new Map();

  // Check if socket is connected
  isConnected(): boolean {
    return this.socket !== null && this.socket.connected;
  }

  // Connect to the WebSocket server
  connect(playerName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // If already connected with the same player name, just resolve
        if (this.socket && this.socket.connected && this.socket.io.opts.query?.playerName === playerName) {
          console.log('Already connected with the same player name');
          resolve();
          return;
        }

        // Close existing connection if any
        if (this.socket) {
          this.socket.close();
        }

        // Create new connection with both websocket and polling transports
        this.socket = io(SOCKET_SERVER_URL, {
          query: { playerName },
          transports: ['websocket', 'polling'],
          reconnectionAttempts: 10,
          reconnectionDelay: 1000,
          timeout: 20000,
          autoConnect: true,
          reconnection: true
        });

        this.socket.on('connect', () => {
          console.log('Connected to WebSocket server');
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          console.error('Connection error:', error);
          reject(error);
        });

        // Set up default event listeners
        this.setupDefaultListeners();

        // Add a timeout to reject if connection takes too long
        setTimeout(() => {
          if (this.socket && !this.socket.connected) {
            reject(new Error('Connection timeout'));
          }
        }, 20000);
      } catch (error) {
        console.error('Error initializing socket:', error);
        reject(error);
      }
    });
  }

  // Disconnect from the WebSocket server
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      console.log('Disconnected from WebSocket server');
    }
  }

  // Create a new lobby
  createLobby(playerName: string, teamName?: string, timeSettings?: any): Promise<{
    lobbyCode: string;
    partnerInviteCode: string;
    opponentInviteCode: string;
  }> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('create_lobby', { playerName, teamName, timeSettings }, (response: {
        success: boolean,
        lobbyCode?: string,
        partnerInviteCode?: string,
        opponentInviteCode?: string,
        error?: string
      }) => {
        if (response.success && response.lobbyCode && response.partnerInviteCode && response.opponentInviteCode) {
          resolve({
            lobbyCode: response.lobbyCode,
            partnerInviteCode: response.partnerInviteCode,
            opponentInviteCode: response.opponentInviteCode
          });
        } else {
          reject(new Error(response.error || 'Failed to create lobby'));
        }
      });
    });
  }

  // Join an existing lobby
  joinLobby(lobbyCode: string, playerName: string): Promise<{ actualLobbyCode?: string, isInviteCode?: boolean }> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('join_lobby', { lobbyCode, playerName }, (response: {
        success: boolean,
        actualLobbyCode?: string,
        isInviteCode?: boolean,
        error?: string
      }) => {
        if (response.success) {
          resolve({
            actualLobbyCode: response.actualLobbyCode,
            isInviteCode: response.isInviteCode
          });
        } else {
          reject(new Error(response.error || 'Failed to join lobby'));
        }
      });
    });
  }

  // Update team name
  updateTeamName(lobbyCode: string, teamNumber: 1 | 2, teamName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('update_team_name', { lobbyCode, teamNumber, teamName }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to update team name'));
        }
      });
    });
  }

  // Set team ready status
  setTeamReady(lobbyCode: string, ready: boolean): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('set_team_ready', { lobbyCode, ready }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to set team ready status'));
        }
      });
    });
  }

  // Switch team
  switchTeam(lobbyCode: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('switch_team', { lobbyCode }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to switch team'));
        }
      });
    });
  }

  // Start the game
  startGame(lobbyCode: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('start_game', { lobbyCode }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to start game'));
        }
      });
    });
  }

  // Find a match for the team
  findMatch(lobbyCode: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('find_match', { lobbyCode }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to find match'));
        }
      });
    });
  }

  // Cancel finding a match
  cancelFindMatch(lobbyCode: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('cancel_find_match', { lobbyCode }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to cancel match finding'));
        }
      });
    });
  }

  // Send a game action
  sendGameAction(action: string, data: any): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        const error = new Error('Socket not connected');
        console.error(error);
        return reject(error);
      }

      console.log(`Sending game action: ${action}`, data);

      // List of actions that should use the game_action event
      const gameActionEvents = ['send_chat_message', 'vote_timeframe', 'join_video_call', 'leave_video_call', 'video_signal'];

      // Check if this action should use the game_action event
      if (gameActionEvents.includes(action)) {
        // Use the generic game_action event
        this.socket.emit('game_action', { action, ...data }, (response: { success: boolean, error?: string }) => {
          if (response?.success) {
            console.log(`Game action ${action} sent successfully`);
            resolve();
          } else {
            console.error(`Failed to send game action ${action}:`, response?.error);
            reject(new Error(response?.error || `Failed to send ${action}`));
          }
        });
        return;
      }

      // For other actions, emit the action directly
      // Create a timeout ID that we can clear if the server responds
      let timeoutId: NodeJS.Timeout;

      this.socket.emit(action, data, (response: { success: boolean, error?: string }) => {
        // Clear the timeout since we got a response
        clearTimeout(timeoutId);

        if (response?.success) {
          console.log(`Game action ${action} successful:`, response);
          resolve();
        } else {
          console.error(`Game action ${action} failed:`, response?.error);
          reject(new Error(response?.error || `Failed to execute ${action}`));
        }
      });

      // Add a timeout in case the server doesn't respond
      timeoutId = setTimeout(() => {
        console.error(`Game action ${action} timed out after 5 seconds`);
        reject(new Error(`${action} timeout`));
      }, 5000);
    });
  }

  // Add an event listener
  on(event: string, callback: (data: any) => void): void {
    if (!this.socket) {
      console.error('Socket not connected');
      return;
    }

    // Store the callback in our listeners map
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)?.push(callback);

    // Add the listener to the socket
    this.socket.on(event, callback);
  }

  // Remove an event listener
  off(event: string, callback?: (data: any) => void): void {
    if (!this.socket) {
      console.error('Socket not connected');
      return;
    }

    if (callback) {
      // Remove specific callback
      this.socket.off(event, callback);

      // Update our listeners map
      const callbacks = this.listeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }
    } else {
      // Remove all callbacks for this event
      this.socket.off(event);
      this.listeners.delete(event);
    }
  }

  // Get the socket ID
  getSocketId(): string | null {
    return this.socket?.id || null;
  }

  // Update the server URL
  updateServerUrl(port: string): void {
    SOCKET_SERVER_URL = `http://localhost:${port}`;
    localStorage.setItem('thunee_server_port', port);
    console.log(`Server URL updated to: ${SOCKET_SERVER_URL}`);
  }

  // Request timeframe options from the server
  requestTimeframeOptions(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        const error = new Error('Socket not connected');
        console.error(error);
        return reject(error);
      }

      console.log('Requesting timeframe options from server');

      this.socket.emit('request_timeframe_options', {}, (response: { success: boolean, error?: string }) => {
        if (response?.success) {
          console.log('Timeframe options request sent successfully');
          resolve();
        } else {
          console.error('Failed to request timeframe options:', response?.error);
          reject(new Error(response?.error || 'Failed to request timeframe options'));
        }
      });
    });
  }

  // Send a custom event to the server
  sendCustomEvent(event: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        const error = new Error('Socket not connected');
        console.error(error);
        return reject(error);
      }

      console.log(`Sending custom event: ${event}`, data);

      // Create a timeout ID that we can clear if the server responds
      let timeoutId: NodeJS.Timeout;

      this.socket.emit(event, data, (response: any) => {
        // Clear the timeout since we got a response
        clearTimeout(timeoutId);

        if (response?.success) {
          console.log(`Custom event ${event} successful:`, response);
          resolve(response);
        } else {
          // Special handling for spectator-related events
          if (event === 'join_as_spectator' && response?.error === 'Game not found') {
            console.error(`Game not found. This could be because the game hasn't started yet or the code is incorrect.`);
            reject(new Error(`Game not found. Please check the game code and make sure the game has started.`));
          } else {
            console.error(`Custom event ${event} failed:`, response?.error);
            reject(new Error(response?.error || `Failed to execute ${event}`));
          }
        }
      });

      // Add a timeout in case the server doesn't respond
      timeoutId = setTimeout(() => {
        console.error(`Custom event ${event} timed out after 5 seconds`);
        reject(new Error(`${event} timeout`));
      }, 5000);
    });
  }

  // Set up default event listeners
  private setupDefaultListeners(): void {
    if (!this.socket) return;

    // Handle disconnection
    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected:', reason);

      // If the disconnection was not intentional, try to reconnect
      if (reason === 'io server disconnect' || reason === 'transport close' || reason === 'ping timeout') {
        console.log('Attempting to reconnect...');
        this.socket?.connect();
      }
    });

    // Handle reconnection
    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`Reconnected after ${attemptNumber} attempts`);
    });

    // Handle reconnection attempts
    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log(`Reconnection attempt ${attemptNumber}`);
    });

    // Handle reconnection errors
    this.socket.on('reconnect_error', (error) => {
      console.error('Reconnection error:', error);
    });

    // Handle reconnection failures
    this.socket.on('reconnect_failed', () => {
      console.error('Failed to reconnect after all attempts');
    });

    // Handle errors
    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }
}

// Create a singleton instance
const socketService = new SocketService();

export default socketService;
